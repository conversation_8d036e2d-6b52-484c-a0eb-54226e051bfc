---
startDate: ""
endDate: ""
---
# 1. OKR设定

- O：
- KR1：
- KR2：

# 2. 项目进度
```dataviewjs
	// 定义 getWeekNumber 函数
	function getWeekNumber(date) {
	    const d = new Date(date);
	    d.setHours(0, 0, 0, 0);
	    d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
	    const week1 = new Date(d.getFullYear(), 0, 4);
	    return 1 + Math.round(((d - week1) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7);
	}
	
	// 获取当前文件的路径信息，提取项目名称
	const currentFile = dv.current().file;
	const currentPath = currentFile.path;
	const projectName = currentPath.split("/")[1].trim();
	
	// 获取当前日期并计算年份和上周周数
	const now = new Date();
	let year = now.getFullYear();
	let weekNumber = getWeekNumber(now) - 1;
	
	// 初始化查找结果
	let krContent = null;
	let foundWeek;
	let foundYear;
	
	// 尝试查找最近的周报（最多回溯4周）
	for (let i = 0; i < 4; i++) {
	    // 处理跨年情况（如果当前是第一周，则上周是去年的最后一周）
	    if (weekNumber < 1) {
	        year--;
	        weekNumber = 52; // ISO周数最大为52或53周
	    }
	    
	    // 动态生成文件名
	    const dynamicFilename = `Replay-${year}-WK${weekNumber.toString().padStart(2, '0')}.md`;
	    const path = `2-项目/${projectName}/3-每周评审/${dynamicFilename}`;
	    const file = app.vault.getAbstractFileByPath(path);
	    
	    if (file) {
	        // 读取并解析文件
	        const content = await dv.io.load(path);
	        const headingRegex = /(?:^|\n)#{1,6}\s*.*?KR进度.*?(?:\n|$)([\s\S]*?)(?=\n?#{1,6}\s|$)/i;
	        const match = content.match(headingRegex);
	        
	        if (match) {
	            krContent = match[1].trim();
	            foundWeek = weekNumber;
	            foundYear = year;
	            break; // 找到有效内容，跳出循环
	        }
	    }
	    
	    // 准备回溯到更早的一周
	    weekNumber--;
	}
	
	// 输出结果
	if (krContent) {
	    dv.paragraph(krContent);
	} else {
	    dv.el("p", "未找到近期项目周报");
	}
```
# 3. 交付异常

```dataviewjs
	// 获取当前系统时间的ISO周数和年份
	function getISOWeek(date) {
		const d = new Date(date);
		d.setHours(0, 0, 0, 0);
		d.setDate(d.getDate() + 4 - (d.getDay() || 7));
		const yearStart = new Date(d.getFullYear(), 0, 1);
		const weekNo = Math.ceil(((d - yearStart) / 86400000 + 1) / 7);
		return [d.getFullYear(), weekNo];
	}
	
	const [targetYear, targetWeek] = getISOWeek(new Date());
	const projectName = dv.current().file.path.split("/")[1].trim();
	const targetFolder = `2-项目/${projectName}/3-每周评审`;
	const tableTitlePattern = "交付异常";
	
	let allTableData = [];
	
	// 第一步：快速筛选符合条件的文件
	const candidateFiles = [];
	for (let file of dv.pages(`"${targetFolder}"`).file) {
		const fileMatch = file.name.match(/^Replay-(\d{4})-WK(\d{2})$/);
		if (!fileMatch) continue;
		
		const fileYear = parseInt(fileMatch[1]);
		const fileWeek = parseInt(fileMatch[2]);
	
		if (fileYear < targetYear || (fileYear === targetYear && fileWeek < targetWeek)) {
			candidateFiles.push({
				file,
				year: fileYear,
				week: fileWeek,
				path: file.path
			});
		}
	}
	
	// 第二步：按时间倒序排序（从最新到最早）
	candidateFiles.sort((a, b) => {
		if (a.year !== b.year) return b.year - a.year;
		return b.week - a.week;
	});
	
	// 第三步：处理符合条件的文件
	for (let candidate of candidateFiles) {
		const { file, year, week, path } = candidate;
		
		// 加载文件内容
		const content = await dv.io.load(path);
		
		// 识别指定标题下的内容区域
		const headingRegex = new RegExp(
			`(?:^|\\n)#+\\s*.*${tableTitlePattern}.*[^\\n]*\\n([\\s\\S]*?)(?=\\n#|$)`, 
			"i"
		);
		
		const match = content.match(headingRegex);
		if (!match || !match[1]) continue;
		
		const sectionContent = match[1].trim();
		
		// 表格解析函数
		const parseMarkdownTables = (markdown) => {
			const tables = [];
			const tableRegex = /\|([^\n]+\|)\s*\n\s*\|(\s*:?[-~]+:?\s*\|)+\s*\n((?:\s*\|[^\n]+\|[^\S\r?\n]*\n?)+)/g;
			
			let tableMatch;
			while ((tableMatch = tableRegex.exec(markdown)) !== null) {
				try {
					const headerRow = tableMatch[1].split('|')
						.map(cell => cell.trim())
						.filter(cell => cell !== '');
					
					// 处理数据行
					const dataRows = tableMatch[3].split('\n')
						.filter(row => row.trim() !== '' && row.includes('|') && !row.startsWith('|--'))
						.map(row => {
							const cells = row.split('|')
								.slice(1, -1)
								.map(cell => {
									const normalized = cell.trim()
										.replace(/<br>/gi, '\n')
										.replace(/\s*\n\s*/g, '\n')
										.trim();
									return normalized;
								});
							return cells;
						})
						.filter(row => row.some(cell => cell !== ''));
					
					if (dataRows.length > 0) {
						tables.push({ header: headerRow, data: dataRows });
					}
				} catch (e) {
					console.warn("表格解析错误:", e);
				}
			}
			return tables;
		};
		
		// 解析表格
		const tables = parseMarkdownTables(sectionContent);
		
		// 处理找到的表格
		if (tables.length > 0) {
			// 创建文件链接
			const weekTag = `${year}-WK${week.toString().padStart(2, '0')}`;
			const fileName = `Review-${weekTag}.md`;
			const fileLink = dv.fileLink(`${targetFolder}/${fileName}`, false, fileName);
			
			// 处理每个表格
			for (const table of tables) {
				const { header, data } = table;
				
				if (allTableData.length === 0) {
					allTableData.push([...header, "回顾链接"]);
				}
				
				data.forEach(row => {
					allTableData.push([...row, fileLink]);
				});
			}
		}
	}   
	
	// 按Blocker ID排序
	if (allTableData.length > 1) {
		const headers = allTableData[0];
		const dataRows = allTableData.slice(1);
		
		const blockerIdColumnIndex = headers.indexOf("来源");
		
		dataRows.sort((a, b) => {
			const blockerIdA = a[blockerIdColumnIndex] || "";
			const blockerIdB = b[blockerIdColumnIndex] || "";
			
			const dateMatchA = blockerIdA.match(/blocker-(\d{8})/);
			const dateMatchB = blockerIdB.match(/blocker-(\d{8})/);
			
			if (dateMatchA && dateMatchB) {
				return dateMatchB[1].localeCompare(dateMatchA[1]);
			} 
			else if (dateMatchA) {
				return -1;
			} 
			else if (dateMatchB) {
				return 1;
			}
			return blockerIdA.localeCompare(blockerIdB);
		});
		
		allTableData = [headers, ...dataRows];
	}
	
	// 输出结果
	if (allTableData.length > 0) {
		dv.table(allTableData[0], allTableData.slice(1));
	} else {
		dv.el("p", "🎉 无历史交付异常数据");
	}
```

# 4. 行动变更

| 变更日期 | 计划/行动名称 | 决策类型 | 决策依据 | 决策失误原因 | 关键证据 | 新行动 |
| ---- | ------- | ---- | ---- | ------ | ---- | --- |
|      |         |      |      |        |      |     |

