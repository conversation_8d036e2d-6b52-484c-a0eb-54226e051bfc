---
search: 验收
---

```dataviewjs
// 获取当前项目名称和路径
const projectName = dv.current().file.path.split("/")[1] || '';
const folderPath = `3-过程资产/${projectName}/阻碍`;

// 获取并清理关键词（支持多关键词空格分隔）
const rawSearch = (dv.current()?.search || "").trim();
const keywords = rawSearch ? rawSearch.toLowerCase().split(/\s+/) : [];

// 多关键词别名匹配函数（AND逻辑）
function matchMultiKeywords(page, keywords) {
    if (keywords.length === 0) return true; // 无关键词时显示所有
    
    const aliases = page.aliases 
        ? (Array.isArray(page.aliases) ? 
            page.aliases.join(" ") : String(page.aliases)).toLowerCase()
        : "";
    
    // 所有关键词都必须出现在别名中
    return keywords.every(k => aliases.includes(k));
}

// 获取并过滤数据
const files = dv.pages(`"${folderPath}"`)
    .filter(p => p.file.name !== dv.current().file.name)
    .filter(p => matchMultiKeywords(p, keywords))
    .sort(p => p.drain_val || 0, "desc");

// 显示结果
if (keywords.length > 0) {
    if (files.length > 0) {
        dv.table(
            ["序号", "文件链接", "别名", "精力消耗", "次数", "创建日期", "耗时", "状态"],
            files.map((p, index) => [
                index + 1,
                p.file.link,
                p.aliases ? (Array.isArray(p.aliases) ? p.aliases.join(", ") : p.aliases) : "-",
                p.drain_val ?? "缺失",
                p.count ?? "缺失",
                p.created_Date ? dv.date(p.created_Date).toFormat("yyyy/MM/dd") : "-",
                p.timeCost ?? "缺失",
                p.status || "缺失"
            ])
        );
    } else {
        dv.paragraph(`🔍 未找到同时包含 ${keywords.map(k => `"${k}"`).join(" 和 ")} 的阻碍记录`);
    }
} else {
    // 无关键词时显示全部
    dv.table(
        ["序号", "文件链接", "别名", "精力消耗", "次数", "创建日期", "耗时", "状态"],
        files.map((p, index) => [
            index + 1,
            p.file.link,
            p.aliases ? (Array.isArray(p.aliases) ? p.aliases.join(", ") : p.aliases) : "-",
            p.drain_val ?? "缺失",
            p.count ?? "缺失",
            p.created_Date ? dv.date(p.created_Date).toFormat("yyyy/MM/dd") : "-",
            p.timeCost ?? "缺失",
            p.status || "缺失"
        ])
    );
}
```