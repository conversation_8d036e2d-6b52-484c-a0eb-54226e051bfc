---
aliases:
  - 验收标准制定方法缺失
created_Date: 2025-06-02
status: 进行中
count: 4
timeCost: 2
drain_val: 4
relation:
  - "[[Replay-2025-WK23]]"
  - "[[blocker-20250710-01]]"
cssclasses:
  - c3
---
# 1. 基础信息

- 现象描述：制定验收标准时无从下手
- 直接影响：周计划环节中断
- 衍生影响：

# 2. 临时方案

| 生效时间                | 目标            | 临时方案描述                                                                                                                                                                                                                                                          | 决策依据                            | 已知风险与局限        | 状态跟踪 | 债务等级 | 知识缺口 |
| ------------------- | ------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------- | -------------- | ---- | ---- | ---- |
| 2025-06-02 10:00    | 定义制定验收标准的初始框架 | 1、明确目标制定方法<br>（1）量化周目标（本身含数字指标）需明确明确定义数据来源、计算方式和达标阈值<br>（2）非量化周目标（产出为文档、决策、流程或行动）用“交付物+确认方式”替代数字，确保结果可客观验证<br>（3）复杂周目标，可拆解为多项必须同时满足的条件，形成检查清单<br>2、根据目标类型制定验收标准<br>（1）完成「具体动作」，使「指标」从「当前值」达到「目标值「（数据来源：「xxx系统/报告」）<br>（2）交付「具体成果物」，并通过「验证方式」/完成「关键行动」，产生「可追溯证据」 | 通过对目标进行分类、结构化处理，可以使验收标准制定的路径更清晰 | 1、适用性较差，执行难度较大 | 已失效  |      |      |
| 2025-06-28 11:30    | 降低临时方案的执行难度   | 1、明确目标制定方法<br>（1）合格目标 = 核心价值 + 可衡量状态/可量化结果 + 时间线<br>2、根据目标制定验收标准<br>（1）当关键细节不明确时，采用「负面清单+关键行为」验证的写法                                                                                                                                                             |                                 | 适用性较差          | 已失效  |      |      |
| 2025-06-30 16:00    | 补充验收标准的适用场景   | 1、明确目标制定方法<br>（1）合格目标 = 核心价值 + 可衡量状态/可量化结果 + 时间线<br>2、根据目标制定验收标准<br>（1）当关键细节不明确时，采用「负面清单+关键行为」验证的写法<br>（2）对于缺乏认知的目标，需要采用“渐进式标准构建法”（框架→主干→协同）制定验收标准（通过分阶段制定验收标准，逐步扩展验证范围）                                                                                        |                                 |                | 生效中  |      |      |
| 2025-07-31 15:55:06 |               |                                                                                                                                                                                                                                                                 |                                 |                |      |      |      |
# 3. 根因分析

- 核心问题是什么？ --> 

# 5. 最终方案（可选）

| 生效时间 | 根本原因 | 方案描述 | 决策依据 | 已知风险与局限 |
| ---- | ---- | ---- | ---- | ------- |
|      |      |      |      |         |
