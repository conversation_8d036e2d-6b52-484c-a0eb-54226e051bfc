/* DataviewJS表格样式 - 按照Markdown表格默认样式显示 */

/* 重置DataviewJS表格的基本样式，使其与Markdown表格一致 */
.block-language-dataviewjs table,
.dataview table {
    /* 基本表格属性 */
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
    font-size: var(--table-text-size, 1em);
    font-family: var(--font-text);

    /* 移除dataview插件的固定布局 */
    table-layout: auto !important;

    /* 边框样式 */
    border: 1px solid var(--table-border-color);
    border-radius: var(--table-border-radius, 4px);
}

/* 表头样式 - 与Markdown表格保持一致 */
.block-language-dataviewjs th,
.dataview th {
    /* 背景和边框 */
    background-color: var(--table-header-background);
    border: 1px solid var(--table-border-color);
    border-bottom: 2px solid var(--table-header-border-color, var(--table-border-color));

    /* 文字样式 */
    font-weight: var(--table-header-weight, 600);
    text-align: left;
    color: var(--table-header-color, var(--text-normal));

    /* 内边距 */
    padding: var(--table-cell-padding, 8px 12px);

    /* 垂直对齐 */
    vertical-align: var(--table-cell-vertical-alignment, middle);

    /* 重置dataview特殊样式 */
    font-size: inherit !important;
    max-width: none !important;
    white-space: normal !important;
}

/* 表格单元格样式 - 与Markdown表格保持一致 */
.block-language-dataviewjs td,
.dataview td {
    /* 边框 */
    border: 1px solid var(--table-border-color);

    /* 内边距 */
    padding: var(--table-cell-padding, 8px 12px);

    /* 文字样式 */
    text-align: left;
    color: var(--text-normal);

    /* 垂直对齐 */
    vertical-align: var(--table-cell-vertical-alignment, middle);

    /* 重置dataview特殊样式 */
    font-weight: normal !important;
    max-width: none !important;
    white-space: normal !important;
    word-wrap: normal !important;
    overflow-wrap: normal !important;
    min-width: auto !important;
}

/* 表格行样式 */
.block-language-dataviewjs tr,
.dataview tr {
    border: none;
}

/* 表格行悬停效果 - 与Markdown表格保持一致 */
.block-language-dataviewjs tbody tr:hover,
.dataview tbody tr:hover {
    background-color: var(--table-row-background-hover);
}

/* 奇偶行样式（如果主题支持） */
.block-language-dataviewjs tbody tr:nth-child(even),
.dataview tbody tr:nth-child(even) {
    background-color: var(--table-row-alt-background, transparent);
}

/* 移除dataview特有的样式覆盖 */
.block-language-dataviewjs .table-view-table,
.dataview .table-view-table {
    width: 100%;
    margin: 0;
}

.block-language-dataviewjs .table-view-table > thead > tr > th,
.dataview .table-view-table > thead > tr > th {
    border: 1px solid var(--table-border-color) !important;
    border-bottom: 2px solid var(--table-header-border-color, var(--table-border-color)) !important;
    font-size: inherit !important;
}

.block-language-dataviewjs .table-view-table > tbody > tr > td,
.dataview .table-view-table > tbody > tr > td {
    border: 1px solid var(--table-border-color) !important;
}

/* 确保表格内的链接样式正常 */
.block-language-dataviewjs table a,
.dataview table a {
    color: var(--link-color);
    text-decoration: var(--link-decoration);
}

.block-language-dataviewjs table a:hover,
.dataview table a:hover {
    color: var(--link-color-hover);
    text-decoration: var(--link-decoration-hover);
}

/* 表格内列表的样式调整 */
.block-language-dataviewjs table ul,
.block-language-dataviewjs table ol,
.dataview table ul,
.dataview table ol {
    margin: 0.2em 0;
    padding-left: 1.5em;
}

/* 响应式表格处理 */
@media (max-width: 768px) {
    .block-language-dataviewjs table,
    .dataview table {
        font-size: 0.9em;
    }

    .block-language-dataviewjs th,
    .block-language-dataviewjs td,
    .dataview th,
    .dataview td {
        padding: 6px 8px;
    }
}